namespace Zin.Api.Middlewares
{
    public class IpRestrictionMiddleware
    {
        private readonly bool _isEnabled;
        private readonly RequestDelegate _next;
        private readonly List<string> _allowedIps;
        private readonly Serilog.ILogger _logger;

        public IpRestrictionMiddleware(RequestDelegate next, IConfiguration configuration, Serilog.ILogger logger)
        {
            _isEnabled = configuration.GetValue<bool>("IpRestriction:Enabled");
            _next = next;
            _allowedIps = configuration.GetSection("IpRestriction:AllowedIps").Get<List<string>>() ?? [];
            _logger = logger;

            if (!_isEnabled)
                _logger.Warning("A restri��o de IP est� desabilitada, permitindo todas as solicita��es.");

            if (_isEnabled && _allowedIps.Count == 0)
                _logger.Warning("A restri��o de IP est� habilitada, mas nenhuma lista de IPs permitidos foi configurada.");
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();

            _logger.Information($"Origem da solicita��o: {remoteIp}");

            if (!_isEnabled)
            {
                _logger.Warning("A solicita��o foi permitida porque a restri��o de IP est� desabilitada, permitindo todas as solicita��es.");
                await _next(context);
                return;
            }

            if (remoteIp == null)
            {
                _logger.Warning("Valor de context.Connection.RemoteIpAddress esta nulo.");
                await DenyAccess(context);
                return;
            }

            // Verifica se o IP remoto est� na lista de IPs permitidos
            if (!_allowedIps.Contains(remoteIp))
            {
                await DenyAccess(context);
                return;
            }

            await _next(context);
        }

        private static async Task DenyAccess(HttpContext context)
        {
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            await context.Response.WriteAsync("Access Denied");
        }
    }
}