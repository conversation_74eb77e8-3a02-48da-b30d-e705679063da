using Microsoft.AspNetCore.Mvc;
using Zin.Api.Shared;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("importacoes")]
    public class ImportacaoController
    (
        IImportacaoService _importacaoService,
        IProcessamentoImportacaoService _processamentoImportacaoService
    ) : ControllerBase
    {         
        [HttpPost("importar")]
        public async Task<IActionResult> ImportaAgregador([FromBody] ImportacaoAgregadorDTO dto)
        {
            var resultado = await _importacaoService.ImportarAgregadorAsync(dto);
            return this.ToActionResult(resultado, locationFactory: () => $"/importacoes/{resultado.Conteudo!.IdImportacao}");
        }

        [HttpPost("processar")]
        public async Task<IActionResult> ProcessarAgregador()
        {
            await _processamentoImportacaoService.ProcessaImportacaoAgregadorTodosClientesAsync();
            return Ok();
        }
    }
}
