
using Npgsql;
using Serilog;
using Serilog.Context;
using Serilog.Events;
using Serilog.Formatting;
using Serilog.Sinks.Http.BatchFormatters;
using System.Diagnostics;
using System.Text.Json;
using Zin.Api.ConfigsMap;
using Zin.Api.Extensions;
using Zin.Application.Configuration;

var builder = WebApplication.CreateBuilder(args);
var cfg = builder.Configuration;

// --- Serilog Configuration ---
var serilogConfig = new LoggerConfiguration()
    .ReadFrom.Configuration(cfg)
    .Enrich.FromLogContext();

var loggingApiUrl = cfg["LoggingApi:Url"];
if (!string.IsNullOrWhiteSpace(loggingApiUrl))
{
    serilogConfig = serilogConfig.WriteTo.Http(
        requestUri: $"{loggingApiUrl}/logs",
        queueLimitBytes: 5_000_000,
        batchFormatter: new Serilog.Sinks.Http.BatchFormatters.JsonArrayBatchFormatter(new LogEventJsonFormatter())
    );
}

serilogConfig = serilogConfig.WriteTo.Console();
Log.Logger = serilogConfig.CreateLogger();
builder.Host.UseSerilog();
builder.Services.AddSingleton(Log.Logger);
// --- End Serilog ---

try
{
    Log.Information("Iniciando a aplicação");

    builder.Services.AdicionaServicosCustomizados(builder.Configuration, Log.Logger);
    builder.Services.Configure<CaminhosConfig>(builder.Configuration.GetSection("Caminhos"));
    builder.Services.AddHttpContextAccessor();
    builder.Services.Configure<List<ExcelColumnMappingConfig>>(builder.Configuration.GetSection("ExcelMappings"));

    var app = builder.Build();

    // --- Logging Middleware ---
    app.Use(async (ctx, next) =>
    {
        var sw = Stopwatch.StartNew();
        var corr = ctx.Request.Headers.TryGetValue("x-correlation-id", out var cid)
            ? cid.ToString()
            : Guid.NewGuid().ToString("N");

        // Use HttpContext.Items to store tenant, as it's the standard place for per-request data.
        var tenant = ctx.Items.TryGetValue("TenantId", out var tenantObj) ? tenantObj as string : "default";

        using (LogContext.PushProperty("correlationId", corr))
        using (LogContext.PushProperty("tenant", tenant))
        {
            ctx.Response.Headers["x-correlation-id"] = corr;
            try
            {
                await next();
                Log.ForContext("route", $"{ctx.Request.Method} {ctx.Request.Path}")
                   .ForContext("status", ctx.Response.StatusCode)
                   .Information("Request handled in {ElapsedMs} ms", sw.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                Log.ForContext("route", $"{ctx.Request.Method} {ctx.Request.Path}")
                   .ForContext("status", 500)
                   .ForContext("exception", new {
                        type = ex.GetType().Name,
                        message = ex.Message,
                        stack = ex.StackTrace
                   }, destructureObjects: true)
                   .Error(ex, "Unhandled exception in request ({ElapsedMs} ms)", sw.ElapsedMilliseconds);
                throw;
            }
        }
    });
    // --- End Middleware ---

    Log.Information($"Ambiente: {app.Environment.EnvironmentName}");

    app.ConfiguraApp();

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "A aplicação falhou ao iniciar");
}
finally
{
    Log.CloseAndFlush();
}


// --- Serilog Custom Formatter ---
public sealed class LogEventJsonFormatter : ITextFormatter
{
    public void Format(LogEvent e, TextWriter output)
    {
        var rendered = e.MessageTemplate.Render(e.Properties);
        var props = new Dictionary<string, object?>(StringComparer.OrdinalIgnoreCase);
        foreach (var kv in e.Properties)
            props[kv.Key] = Simplify(kv.Value);

        props.TryGetValue("tenant", out var tenant);
        props.TryGetValue("correlationId", out var correlationId);
        
        var act = Activity.Current;
        var trace = new {
            correlationId,
            traceId = act?.TraceId.ToString(),
            spanId  = act?.SpanId.ToString(),
        };

        var exceptionProps = props.ContainsKey("exception") ? props["exception"] : null;

        var payload = new {
            ts = e.Timestamp.UtcDateTime,
            level = e.Level.ToString(),
            message = rendered,
            props,
            exception = exceptionProps,
            trace,
            tenant,
            host = Environment.MachineName,
            schemaVersion = 1
        };
        output.Write(JsonSerializer.Serialize(payload));
    }

    private static object? Simplify(LogEventPropertyValue v) => v switch
    {
        ScalarValue s => s.Value,
        SequenceValue seq => seq.Elements.Select(Simplify).ToArray(),
        StructureValue str => str.Properties.ToDictionary(p => p.Name, p => Simplify(p.Value)),
        DictionaryValue dict => dict.Elements.ToDictionary(e => e.Key.Value?.ToString() ?? "", e => Simplify(e.Value)),
        _ => v.ToString()
    };
}
