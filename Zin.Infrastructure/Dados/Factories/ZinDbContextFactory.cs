using Microsoft.EntityFrameworkCore;
using Serilog;
using Zin.Infrastructure.Services;

namespace Zin.Infrastructure.Dados.Factories
{
    public class ZinDbContextFactory(
            IClienteProvider clienteProvider,
            IClienteConnectionService clienteConnectionService,
            ILogger logger) : IDbContextFactory<ZinDbContext>
    {
        private readonly IClienteProvider _clienteProvider = clienteProvider;
        private readonly IClienteConnectionService _clienteConnectionService = clienteConnectionService;
        private readonly ILogger _logger = logger;

        public ZinDbContext CreateDbContext()
        {
            _logger.Debug("Iniciando criação do ZinDbContext.");

            var cliente = _clienteProvider.GetCliente();

            if (string.IsNullOrEmpty(cliente.BaseDados))
            {
                _logger.Error("O cliente selecionado não possui uma base de dados configurada.");

                throw new InvalidOperationException("O cliente selecionado não possui uma base de dados configurada. " +
                    "Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente " +
                    "e que o cliente tem uma base de dados definida.");
            }

            var nomeBaseDados = cliente.BaseDados;
            _logger.Debug("Nome da base de dados do cliente: {NomeBaseDados}", nomeBaseDados);

            var connectionString = _clienteConnectionService.BuscaClienteConnectionString(nomeBaseDados!);

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.Error("A connection string para a base de dados '{NomeBaseDados}' do cliente não foi encontrada nas configurações da API.", nomeBaseDados);

                throw new InvalidOperationException($"A connection string para a base de dados '{nomeBaseDados}' do cliente não foi encontrada nas configurações da API. " +
                    "Verifique se a configuração da connection string está correta no appsettings ou no provedor de configurações.");
            }

            _logger.Debug("ConnectionString obtida para a base {NomeBaseDados}: {ConnectionString}", nomeBaseDados, connectionString);

            var optionsBuilder = new DbContextOptionsBuilder<ZinDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            _logger.Information("ZinDbContext criado com sucesso para a base {NomeBaseDados}.", nomeBaseDados);
            return new ZinDbContext(optionsBuilder.Options);
        }
    }
}
