using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class PessoaRepository(IUnitOfWork unitOfWork) : IPessoaRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Pessoa entidade)
        {
            Context.Pessoas.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Pessoa>> BuscarAsync(Expression<Func<Pessoa, bool>> predicado)
        {
            return await Context.Pessoas.Where(predicado).ToListAsync();
        }

        public async Task<Pessoa?> BuscarPorDocumentoAsync(string documento)
        {
            // Busca em PessoaFisica (CPF)
            var pessoaFisica = await Context.PessoasFisicas
                .FirstOrDefaultAsync(pf => pf.Cpf == documento);

            if (pessoaFisica != null)
                return pessoaFisica;

            // Busca em PessoaJuridica (CNPJ)
            var pessoaJuridica = await Context.PessoasJuridicas
                .FirstOrDefaultAsync(pj => pj.Cnpj == documento);

            if (pessoaJuridica != null)
                return pessoaJuridica;

            throw new EntidadeNaoEncontradaExcecao($"Pessoa com documento {documento} não encontrada.");
        }

        public async Task<Pessoa?> BuscarPorIdAsync(int id)
        {
            var pessoa = await Context.Pessoas
                .Include(p => p.PessoasEnderecos)
                    .ThenInclude(pe => pe.Endereco)
                .Include(p => p.PessoasContatos)
                    .ThenInclude(pc => pc.Contato)
                // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
                //.ThenInclude(c => c.Tipos)
                .Include(p => p.PessoasContatos)
                    .ThenInclude(pc => pc.Contato)
                // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
                //.ThenInclude(c => c.Tipos)
                .Include(p => p.DadosBancarios)
                    .ThenInclude(b => b.DadoBancario)
                    .ThenInclude(db => db.Banco)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (pessoa is null)
                throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");

            return pessoa;
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.Pessoas.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");
            Context.Pessoas.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarVariosAsync(IEnumerable<Pessoa> entidades)
        {
            Context.Pessoas.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(Pessoa entidade)
        {
            await Context.Pessoas.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Pessoa> entidades)
        {
            await Context.Pessoas.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return [.. entidades.Select(e => e.Id)];
        }

        public async Task<IEnumerable<Pessoa>> ListarAsync()
        {
            return await Context.Pessoas.ToListAsync();
        }

        public async Task<Pessoa?> BuscarComContatosPorIdAsync(int id)
        {
            return await Context.Pessoas
                .Include(p => p.PessoasContatos)
                .ThenInclude(pc => pc.Contato)
                .FirstOrDefaultAsync(p => p.Id == id);
        }
    }
}
