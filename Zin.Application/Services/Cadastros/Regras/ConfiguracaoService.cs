using AutoMapper;
using Zin.Application.DTOs.Configuracao;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros.Regras
{
    public class ConfiguracaoService(
        IConfiguracaoRepository configuracaoRepositorio,
        IMapper mapper) : IConfiguracaoService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IConfiguracaoRepository _repository = configuracaoRepositorio;

        public async Task<ConfiguracaoDto?> ObterConfiguracaPorIdAsync(Guid id)
        {
            var configuracao = await _repository.BuscarPorIdAsync(id);
            return _mapper.Map<ConfiguracaoDto>(configuracao);
        }
      
        public async Task<IEnumerable<ConfiguracaoDto>> ListarConfiguracaoAsync()
        {
            var configuracoes = await _repository.BuscarAsync();
            return _mapper.Map<IEnumerable<ConfiguracaoDto>>(configuracoes);
        } 

        public async Task<ConfiguracaoDto> CriarConfiguracaoAsync(CriaConfiguracaoDto dto)
        {
            dto.CriadoEm = DateTime.UtcNow;
            var configuracao = _mapper.Map<Configuracao>(dto);

            var idGerado = await _repository.InserirAsync(configuracao);
            var criado = await _repository.BuscarPorIdAsync(idGerado);
            return _mapper.Map<ConfiguracaoDto>(criado);
        }

        public async Task AtualizarConfiguracaoAsync(Guid id, AtualizaConfiguracaoDto dto)
        {
            var configuracaoExistente = await _repository.BuscarPorIdAsync(id);

            if (configuracaoExistente == null)
                throw new Exception($"Configuração com ID {id} não encontrado.");

            dto.AtualizadoEm = DateTime.UtcNow;
            _mapper.Map(dto, configuracaoExistente);

            await _repository.AtualizarAsync(configuracaoExistente);
        }

        public async Task RemoverConfiguracaoAsync(Guid id) => await _repository.DeletarAsync(id);

        public async Task<IEnumerable<Configuracao?>> BuscarPorTipoProcessamentoAsync(TipoProcessamento tipoProcessamento)
        {
            return await _repository.BuscarPorTipoProcessamentoAsync(tipoProcessamento);
        }

        public async Task<RegraDto?> ObterRegraPorIdAsync(Guid idRegra) 
        {
            var regra = await _repository.BuscarRegraPorIdAsync(idRegra);
            return _mapper.Map<RegraDto>(regra);
        }

        public async Task AtualizarRegraAsync(Guid id, AtualizaRegraDto dto)
        {
            var regraExistente = await _repository.BuscarRegraPorIdAsync(id);

            if (regraExistente == null)
                throw new Exception($"Regra com ID {id} não encontrado.");

            _mapper.Map(dto, regraExistente);

            await _repository.AtualizarRegraAsync(regraExistente);

        }

        public async Task RemoverRegraAsync(Guid idRegra) => await _repository.DeletarRegraAsync(idRegra);
    }
}
