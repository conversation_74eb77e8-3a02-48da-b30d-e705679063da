using AutoMapper;
using Zin.Application.DTOs.Itens;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class ItemService(IItemRepository itemRepository, IMapper mapper) : IItemService
    {
        private readonly IItemRepository _itemRepository = itemRepository;
        private readonly IMapper _mapper = mapper;

        public async Task<ItemDto> CriarItemAsync(CriaItemDto dto)
        {
            var item = _mapper.Map<Item>(dto);
            var id = await _itemRepository.InserirAsync(item);
            var novoItem = await _itemRepository.BuscarPorIdAsync(id);
            return _mapper.Map<ItemDto>(novoItem);
        }

        public async Task AtualizarItemAsync(int id, AtualizaItemDto dto)
        {
            var item = await _itemRepository.BuscarPorIdAsync(id);

            if (item == null)
                throw new EntidadeNaoEncontradaExcecao($"Item com ID {id} não encontrado.");

            _mapper.Map(dto, item);
            await _itemRepository.AtualizarAsync(item);
        }

        public async Task<IEnumerable<ItemDto>> ListarItensAsync()
        {
            var itens = await _itemRepository.ListarAsync();
            return _mapper.Map<IEnumerable<ItemDto>>(itens);
        }

        public async Task<ItemDto?> ObterItemPorIdAsync(int id)
        {
            var item = await _itemRepository.BuscarPorIdAsync(id);
            return _mapper.Map<ItemDto>(item);
        }

        public async Task RemoverItemAsync(int id)
        {
            await _itemRepository.DeletarAsync(id);
        }

        public async Task<IEnumerable<ItemDto>> ListarItensPorAgregadorAsync(int agregadorId)
        {
            var itens = await _itemRepository.BuscarAsync(i => i.IdAgregador == agregadorId);
            return _mapper.Map<IEnumerable<ItemDto>>(itens);
        }

    }
}
