using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Helpers.Clientes;
using Zin.Helpers.Clientes.Models;
using Zin.Infrastructure.Services;

namespace Zin.Application.Services.Importacoes
{
    public class ProcessamentoImportacaoService
    (
        IHttpContextAccessor _httpContextAccessor,
        IServiceScopeFactory _scopeFactory,
        IImportacaoRepository _importacaoRepository,
        IFilaProcessamentoRepository _filaProcessamentoRepository,
        IImportacaoAgregadorService _agregadorService,
        IProcessosService _processosService,
        ILogger _logger
    ) : IProcessamentoImportacaoService
    {
        public async Task ProcessaImportacaoAgregadorTodosClientesAsync()
        {
            var idClientesComImportacoesPendentes = _importacaoRepository.BuscarAsync(i => i.Status == StatusImportacao.AguardandoProcessamento).Result
                .Select(i => i.IdCliente)
                .Distinct()
                .ToList();

            if (idClientesComImportacoesPendentes.Count == 0)
            {
                _logger.Information("Nenhuma importação pendente encontrada.");
                return;
            }

            var clientesAImportar = ClienteHelper.ObterClientes(_httpContextAccessor.HttpContext!)
                .Where(c => idClientesComImportacoesPendentes.Contains(c.Id))
                .ToList();

            foreach (var cliente in clientesAImportar)
            {
                _logger.Information($"Iniciando processamento de importações para o cliente: {cliente.Id}");
                using (var scope = _scopeFactory.CreateScope())
                {
                    var clienteProvider = scope.ServiceProvider.GetRequiredService<IClienteProvider>();

                    clienteProvider.SetCliente(cliente);

                    var importacaoService = scope.ServiceProvider.GetRequiredService<IProcessamentoImportacaoService>();

                    await importacaoService.ProcessaImportacaoAgregadorPorClienteAsync(cliente);
                }
                _logger.Information($"Processamento de importações concluído para o cliente: {cliente.Id}");
            }
        }

        public async Task ProcessaImportacaoAgregadorPorClienteAsync(Cliente cliente)
        {
            var importacoesAgregadores = await _importacaoRepository.BuscarImportacoesPendentesPorClienteAsync(cliente.Id);
            foreach (var importacaoAgregador in importacoesAgregadores)
                await ProcessarAgregadorDaImportacaoAsync(importacaoAgregador);
        }

        private async Task ProcessarAgregadorDaImportacaoAsync(ImportacaoAgregador importacaoPendente)
        {
            try
            {
                _logger.Information($"Iniciando processamento da importação: {importacaoPendente.Id}");

                var dto = JsonConvert.DeserializeObject<ImportacaoAgregadorDTO>(importacaoPendente.DadosAgregador);

                Agregador agregador = await _agregadorService.IniciarProcessamentoAsync(dto!);

                importacaoPendente.FinalizarProcessamento(agregador);

                await _importacaoRepository.AtualizarAsync(importacaoPendente);

                await AdicionarItensNaFilaProcessamentoAsync(agregador);

                await _processosService.ProcessaTodosProcessosAsync();

                _logger.Information($"Processamento da importação concluído: {importacaoPendente.Id}");
            }
            catch (Exception ex)
            {
                importacaoPendente.Erro(ex.Message);
                await _importacaoRepository.AtualizarAsync(importacaoPendente);
                _logger.Error(ex, $"Erro ao processar importação: {importacaoPendente.Id} / EX: {ex.Message}");
            }
        }

        private async Task AdicionarItensNaFilaProcessamentoAsync(Agregador agregador)
        {
            foreach (var item in agregador.Itens)
            {
                var filaProcessamento = new FilaProcessamento(item);
                await _filaProcessamentoRepository.InserirAsync(filaProcessamento);
            }
        }
    }
}

