using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class PossivelRessarcimentoService : IPossivelRessarcimentoService
    {
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IPagamentoRepository _pagamentoRepositorio;
        private readonly IRessarcimentoRepository _ressarcimentoRepositorio;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly ILogger<PossivelRessarcimentoService> _logger;
        private readonly ICondicaoComparadorService _condicaoComparadorService;
        private readonly IConfiguracaoService _configuracaoService;

        public PossivelRessarcimentoService(
            IItemVersaoRepository itemVersaoRepositorio,
            IPagamentoRepository pagamentoRepositorio,
            IRessarcimentoRepository ressarcimentoRepositorio,
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            IItemRepository itemRepositorio,
            IAgregadorRepository agregadorRepositorio,
            ICondicaoComparadorService condicaoComparadorService,
            IConfiguracaoService configuracaoService,
            ILogger<PossivelRessarcimentoService> logger)
        {
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _pagamentoRepositorio = pagamentoRepositorio;
            _ressarcimentoRepositorio = ressarcimentoRepositorio;
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _itemRepositorio = itemRepositorio;
            _logger = logger;
            _condicaoComparadorService = condicaoComparadorService;
            _configuracaoService = configuracaoService;

        }

        public async Task ProcessarAsync(int idAgregador)
        {
            var itensVersao = await _itemVersaoRepositorio.BuscarPorAgregadorIdAsync(idAgregador);

            foreach (var itemVersao in itensVersao)
            {
                try
                {
                    // Só considera exclusões
                    if (itemVersao.TipoMovimento != TipoMovimentoItem.Exclusao)
                        continue;

                    // Busca a autorização correspondente pela DataHoraAutorizacao
                    var autorizacao = itensVersao.FirstOrDefault(v =>
                        v.TipoMovimento == TipoMovimentoItem.Autorizacao &&
                        v.DataHoraAutorizacao == itemVersao.DataHoraAutorizacao
                    );
                    if (autorizacao == null)
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.Erro,
                            "Não há autorização correspondente para essa exclusão.",
                            new List<Divergencia>(),
                            new List<Condicao>(),
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Tem que ter pagamento efetuado para a autorização
                    var pagamentos = await _pagamentoRepositorio.BuscarPagamentosPorItemVersaoIdAsync(autorizacao.Id);
                    if (!pagamentos.Any())
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.Processado,
                            "Não há pagamento para a autorização correspondente.",
                            new List<Divergencia>(),
                            new List<Condicao>(),
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Não pode já ter ressarcimento
                    var ressarcimentos = await _ressarcimentoRepositorio.BuscarPorItemVersaoIdAsync(autorizacao.Id);
                    if (ressarcimentos.Any())
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.RessarcimentoEfetuado,
                            "Ressarcimento já realizado para esta autorização.",
                            new List<Divergencia>(),
                            new List<Condicao>(),
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Verificação dinâmica das regras da configuração
                    var configuracoes = await _configuracaoService.BuscarPorTipoProcessamentoAsync(TipoProcessamento.Ressarcimento);
                    var apto = true;
                    var condicoes = new List<Condicao>();
                    foreach (var config in configuracoes)
                    {
                        if (config == null)
                        {
                            // Configuração padrão, não deve ser processada
                            continue;
                        }
                        else
                        {
                            var resultadosRegras = await _condicaoComparadorService.VerificaAsync(config.Id, itemVersao);
                            if (resultadosRegras != null)
                            {
                                foreach (var resultadoRegra in resultadosRegras)
                                {
                                    if (!resultadoRegra.Apto)
                                    {
                                        apto = false;
                                    }

                                    var condicao = new Condicao
                                    {
                                        Apto = resultadoRegra.Apto,
                                        TipoConfiguracao = resultadoRegra.TipoConfiguracao,
                                        Regra = resultadoRegra.NomeRegra,
                                        DataRegistro = DateTime.UtcNow,
                                        Decisao = null,
                                        DataDecisao = null,
                                        UsuarioDecisao = null
                                    };
                                    condicoes.Add(condicao);
                                }
                            }
                        }
                    }

                    // Se passou por todas as condições, é possível pagamento
                    if (!apto)
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                                     itemVersao,
                                     TipoProcessamento.Ressarcimento,
                                     StatusProcessamento.PossivelRessarcimento,
                                     "Item Versão esta inapto para ressarcimento conforme condições",
                                     new List<Divergencia>(),
                                     condicoes,
                                     null,
                                     _itemVersaoRepositorio
                                 );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Caso apto para ressarcimento
                    await RegistroProcessamentoHelper.RegistrarAsync(
                        itemVersao,
                        TipoProcessamento.Ressarcimento,
                        StatusProcessamento.PossivelRessarcimento,
                        $"ItemVersao apto para ressarcimento. Valor: {autorizacao.ValorTotal}, Item: {autorizacao.Item?.Id}",
                        new List<Divergencia>(),
                        new List<Condicao>(),
                        _registroProcessamentoRepositorio,
                        _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, TipoProcessamento.Ressarcimento, StatusProcessamento.Erro);
                    await _itemVersaoRepositorio.AtualizarAsync(itemVersao);

                    if (itemVersao.Item != null)
                    {
                        itemVersao.Item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(itemVersao.Item);
                    }
                    _logger.LogError(ex, $"Erro ao processar versão {itemVersao.Id} do item {itemVersao.IdItem} no agregador {idAgregador}");
                }
                await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
            }
        }
    }
}