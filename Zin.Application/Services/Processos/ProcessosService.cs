using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.Processos;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class ProcessosService : IProcessosService
    {
        private readonly IAgregadorRepository _agregadorRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IFilaProcessamentoRepository _filaProcessamentoRepository;

        private readonly PossivelItemDuplicadoService _itemDuplicadoService;
        private readonly PossivelPagamentoDuplicadoService _pagamentoDuplicadoService;
        private readonly PossivelPagamentoService _possivelPagamentoService;
        private readonly PossivelRessarcimentoService _possivelRessarcimentoService;
        private readonly ILogger<ProcessosService> _logger;

        public ProcessosService(
            IAgregadorRepository agregadorRepositorio,
            IItemRepository itemRepositorio,
            IItemVersaoRepository itemVersaoRepositorio,
            PossivelItemDuplicadoService itemDuplicadoService,
            PossivelPagamentoDuplicadoService pagamentoDuplicadoService,
            PossivelPagamentoService possivelPagamentoService,
            PossivelRessarcimentoService possivelRessarcimentoService,
            IFilaProcessamentoRepository filaProcessamentoRepository,
            ILogger<ProcessosService> logger
        )
        {
            _agregadorRepositorio = agregadorRepositorio;
            _itemRepositorio = itemRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _itemDuplicadoService = itemDuplicadoService;
            _pagamentoDuplicadoService = pagamentoDuplicadoService;
            _possivelPagamentoService = possivelPagamentoService;
            _possivelRessarcimentoService = possivelRessarcimentoService;
            _filaProcessamentoRepository = filaProcessamentoRepository;
            _logger = logger;
        }

        public async Task ProcessaItemDuplicadoAsync()
        {
            var listaFila = await _filaProcessamentoRepository.ListarPorStatusAsync(StatusFilaProcessamento.AProcessar,StatusFilaProcessamento.Erro);
            foreach (var itemfila in listaFila)
            {
                try
                {
                    await _itemDuplicadoService.ProcessarAsync(itemfila.IdAgregador, itemfila.IdItem);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        itemfila.IdAgregador, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Processado;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar ItemDuplicado para item {itemfila.Item?.Descricao} do agregador {itemfila.IdAgregador}");
                    await _agregadorRepositorio.AtualizarStatusAsync(itemfila.IdAgregador, StatusProcessamento.Erro);
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Erro;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
            }

        }

        public async Task ProcessaPagamentoDuplicadoAsync()
        {
            var listaFila = await _filaProcessamentoRepository.ListarPorStatusAsync(StatusFilaProcessamento.AProcessar, StatusFilaProcessamento.Erro);
            foreach (var itemfila in listaFila)
            {
                try
                {
                    await _pagamentoDuplicadoService.ProcessarAsync(itemfila.IdAgregador);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        itemfila.IdAgregador, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Processado;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PagamentoDuplicado para item {itemfila.Item?.Descricao} do agregador {itemfila.IdAgregador}");
                    await _agregadorRepositorio.AtualizarStatusAsync(itemfila.IdAgregador, StatusProcessamento.Erro);
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Erro;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
            }
        }

        public async Task ProcessaPossivelPagamentoAsync()
        {
            var listaFila = await _filaProcessamentoRepository.ListarPorStatusAsync(StatusFilaProcessamento.AProcessar, StatusFilaProcessamento.Erro);
            foreach (var itemfila in listaFila)
            {
                try
                {
                    await _possivelPagamentoService.ProcessarAsync(itemfila.IdAgregador);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        itemfila.IdAgregador, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Processado;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PossivelPagamento para item {itemfila.Item?.Descricao} do agregador {itemfila.IdAgregador}");
                    await _agregadorRepositorio.AtualizarStatusAsync(itemfila.IdAgregador, StatusProcessamento.Erro);
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Erro;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
            }
        }

        public async Task ProcessaPossivelRessarcimentoAsync()
        {
            var listaFila = await _filaProcessamentoRepository.ListarPorStatusAsync(StatusFilaProcessamento.AProcessar, StatusFilaProcessamento.Erro);
            foreach (var itemfila in listaFila)
            {
                try
                {
                    await _possivelRessarcimentoService.ProcessarAsync(itemfila.IdAgregador);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        itemfila.IdAgregador, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Processado;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PossivelRessarcimento para item {itemfila.Item?.Descricao} do agregador {itemfila.IdAgregador}");
                    await _agregadorRepositorio.AtualizarStatusAsync(itemfila.IdAgregador, StatusProcessamento.Erro);
                    itemfila.StatusFilaProcessamento = StatusFilaProcessamento.Erro;
                    await _filaProcessamentoRepository.AtualizarAsync(itemfila);
                }
            }
        }

        public async Task ProcessaTodosProcessosAsync()
        {
            try
            {
                await ProcessaItemDuplicadoAsync();
                await ProcessaPagamentoDuplicadoAsync();
                await ProcessaPossivelPagamentoAsync();
                await ProcessaPossivelRessarcimentoAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erro ao processar todos os processos para agregadores");
            }
        }
    }
}